#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试编码修复是否有效
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入修复后的模块
from src.utils import safe_print
from src.ai_handler import safe_print as ai_safe_print

def test_encoding_fix():
    """测试编码修复"""
    print("开始测试编码修复...")
    
    # 测试包含中文的字符串
    test_messages = [
        "测试中文输出: 这是一个包含中文的测试消息",
        "函数 get_ai_analysis 第 1/5 次尝试失败: UnicodeEncodeError - 'ascii' codec can't encode characters",
        "   [AI分析] 开始分析商品 #960014044875 (含 6 张图片)...",
        "   [AI分析] 标题: 出一台MacBook Air M1芯片，16G内存，512G SSD",
        "将在 10 秒后重试...",
        "函数 get_ai_analysis 在 5 次尝试后彻底失败。"
    ]
    
    print("\n=== 测试 utils.safe_print ===")
    for i, msg in enumerate(test_messages, 1):
        try:
            print(f"测试 {i}: ", end="")
            safe_print(msg)
            print("✓ 成功")
        except Exception as e:
            print(f"✗ 失败: {e}")
    
    print("\n=== 测试 ai_handler.safe_print ===")
    for i, msg in enumerate(test_messages, 1):
        try:
            print(f"测试 {i}: ", end="")
            ai_safe_print(msg)
            print("✓ 成功")
        except Exception as e:
            print(f"✗ 失败: {e}")
    
    print("\n=== 测试模拟错误情况 ===")
    try:
        # 模拟一个包含中文的异常
        class TestException(Exception):
            def __str__(self):
                return "这是一个包含中文的错误信息: MacBook Air M1芯片"
        
        raise TestException()
    except Exception as e:
        try:
            error_msg = f"函数 test_function 第 1/5 次尝试失败: {type(e).__name__} - {e}"
            safe_print(error_msg)
            print("✓ 异常处理成功")
        except Exception as ex:
            print(f"✗ 异常处理失败: {ex}")
    
    print("\n编码测试完成!")

if __name__ == "__main__":
    test_encoding_fix()
