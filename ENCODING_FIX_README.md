# Unicode编码错误修复说明

## 问题描述

在Windows系统上运行AI闲鱼监控程序时，遇到了以下错误：

```
UnicodeEncodeError - 'ascii' codec can't encode characters in position 7-10: ordinal not in range(128)
```

这个错误发生在程序试图输出包含中文字符的日志信息时，特别是在AI分析失败重试过程中。

## 问题原因

1. **系统编码不一致**：Windows系统的locale设置为`cp936`（GBK编码），但Python默认使用UTF-8编码
2. **输出流编码问题**：在某些情况下，标准输出流会回退到ASCII编码，无法处理中文字符
3. **错误处理中的编码问题**：重试装饰器在输出错误信息时没有考虑编码安全性

## 解决方案

### 1. 程序启动时的编码设置

在`spider_v2.py`开头添加了`fix_encoding()`函数：
- 设置`PYTHONIOENCODING=utf-8`环境变量
- 重新配置标准输出流和错误流的编码为UTF-8

### 2. 安全的print函数

在`src/utils.py`和`src/ai_handler.py`中添加了`safe_print()`函数：
- 自动捕获`UnicodeEncodeError`异常
- 使用`errors='replace'`参数进行安全的编码转换

### 3. 错误处理的编码保护

修改了`src/utils.py`中的重试装饰器：
- 对所有包含中文的print语句添加了编码保护
- 确保错误信息能够安全输出

### 4. 启动脚本

创建了便捷的启动脚本：
- `run.bat`：Windows批处理脚本，自动设置编码环境
- `run.sh`：Linux/Mac shell脚本，设置UTF-8环境变量

## 使用方法

### Windows用户

1. **推荐方式**：使用批处理脚本
   ```cmd
   run.bat
   ```

2. **手动方式**：设置编码后运行
   ```cmd
   chcp 65001
   set PYTHONIOENCODING=utf-8
   python spider_v2.py
   ```

### Linux/Mac用户

1. **推荐方式**：使用shell脚本
   ```bash
   ./run.sh
   ```

2. **手动方式**：设置环境变量后运行
   ```bash
   export PYTHONIOENCODING=utf-8
   export LANG=zh_CN.UTF-8
   python spider_v2.py
   ```

## 测试验证

运行测试脚本验证修复效果：
```bash
python test_encoding.py
```

## 修改的文件

1. `spider_v2.py` - 添加了启动时的编码修复
2. `src/utils.py` - 添加了safe_print函数和编码保护
3. `src/ai_handler.py` - 添加了safe_print函数
4. `run.bat` - Windows启动脚本
5. `run.sh` - Linux/Mac启动脚本
6. `test_encoding.py` - 编码测试脚本

## 注意事项

1. 这个修复是向后兼容的，不会影响现有功能
2. 如果仍然遇到编码问题，请确保：
   - 使用提供的启动脚本
   - 检查系统的区域设置
   - 确保终端支持UTF-8编码
3. 在Docker环境中，这些修复同样有效

## 技术细节

修复采用了多层防护策略：
1. **环境层**：设置正确的环境变量
2. **系统层**：重新配置输出流编码
3. **应用层**：使用安全的print函数
4. **异常层**：在错误处理中添加编码保护

这确保了在各种环境下都能正确处理中文字符输出。
